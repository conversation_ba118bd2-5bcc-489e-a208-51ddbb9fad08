<?php $__env->startSection('cssprivate'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('header'); ?>
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="<?php echo e(url('/ap511-check-invoice/dashboard')); ?>"><i class="fa fa-dashboard"></i> Dashboard</a>
            </li>
            <li>
                <a href="<?php echo e(url('/ap511-check-invoice/list')); ?>"><i class="fa fa-list"></i> Invoice List</a>
            </li>
        </ul>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php if(Auth::user()): ?>
    <div class="row">
        <div class="col-lg-12 text-right">
            <h5><strong>Requested on:</strong> <?php echo e(Carbon\Carbon::now()->format('d-M-Y H:i:s')); ?></h5>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="block">
        <div class="block-header bg-primary-dark">
            <h3 class="block-title">
                <i class="fa fa-filter"></i> Filter Options
            </h3>
        </div>
        <div class="block-content">
            <div class="row items-push">
                <div class="col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label for="filterYear" class="font-w600">Year:</label>
                        <select id="filterYear" name="year" class="form-control">
                            <?php $__currentLoopData = $availableYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $availableYear): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($availableYear); ?>" <?php echo e($availableYear == $year ? 'selected' : ''); ?>>
                                    <?php echo e($availableYear); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label for="filterMonth" class="font-w600">Month:</label>
                        <select id="filterMonth" name="month" class="form-control">
                            <option value="">All Months</option>
                            <?php for($i = 1; $i <= 12; $i++): ?>
                                <option value="<?php echo e($i); ?>" <?php echo e($i == $month ? 'selected' : ''); ?>>
                                    <?php echo e(Carbon\Carbon::create()->month($i)->format('F')); ?>

                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                </div>
                <div class="col-sm-12 col-lg-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="applyFilter" class="btn btn-success btn-block">
                            <i class="fa fa-search"></i> Apply Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row items-push">
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-primary">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="fa fa-file-text-o fa-3x text-white-op" style="margin-bottom: 15px;"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="totalInvoices" class="h2"><?php echo e(number_format($statistics['total_invoices'])); ?></span>
                        </div>
                        <div class="text-white-op">Total Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-success">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="fa fa-check-circle fa-3x text-white-op" style="margin-bottom: 15px;"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="ap511Invoices" class="h2"><?php echo e(number_format($statistics['ap511_invoices'])); ?></span>
                        </div>
                        <div class="text-white-op">AP511 Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-warning">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="fa fa-times-circle fa-3x text-white-op" style="margin-bottom: 15px;"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="nonAp511Invoices" class="h2"><?php echo e(number_format($statistics['non_ap511_invoices'])); ?></span>
                        </div>
                        <div class="text-white-op">Non-AP511 Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-info">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="fa fa-percent fa-3x text-white-op" style="margin-bottom: 15px;"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="ap511Percentage" class="h2">
                                <?php echo e($statistics['total_invoices'] > 0 ? number_format(($statistics['ap511_invoices'] / $statistics['total_invoices']) * 100, 1) : 0); ?>%
                            </span>
                        </div>
                        <div class="text-white-op">AP511 Percentage</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Breakdown -->
    <div class="block">
        <div class="block-header bg-gray-darker">
            <h3 class="block-title">
                <i class="fa fa-bar-chart"></i> Monthly Breakdown
            </h3>
        </div>
        <div class="block-content">
            <div class="table-responsive">
                <table id="monthlyBreakdownTable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Month</th>
                            <th class="text-center">Total Invoices</th>
                            <th class="text-center">AP511 Invoices</th>
                            <th class="text-center">Non-AP511 Invoices</th>
                            <th class="text-center">AP511 %</th>
                        </tr>
                    </thead>
                    <tbody id="monthlyBreakdownTableBody">
                        <?php $__currentLoopData = $statistics['monthly_breakdown']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $monthData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center font-w600 text-primary"><?php echo e($monthData['month_name']); ?></td>
                            <td class="text-center">
                                <span class="badge badge-primary"><?php echo e(number_format($monthData['total'])); ?></span>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-success"><?php echo e(number_format($monthData['ap511'])); ?></span>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-danger"><?php echo e(number_format($monthData['non_ap511'])); ?></span>
                            </td>
                            <td class="text-center">
                                <span class="font-w600 text-<?php echo e($monthData['total'] > 0 && ($monthData['ap511'] / $monthData['total']) * 100 > 50 ? 'success' : 'warning'); ?>">
                                    <?php echo e($monthData['total'] > 0 ? number_format(($monthData['ap511'] / $monthData['total']) * 100, 1) : 0); ?>%
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if(count($statistics['monthly_breakdown']) == 0): ?>
                        <tr>
                            <td colspan="5" class="text-center text-muted py-20">
                                <i class="fa fa-info-circle"></i> No data available for the selected period
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<style>
/* Enhanced Statistics Cards */
.statistics-card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.statistics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.statistics-card .block-content {
    position: relative;
}

.statistics-card .fa {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.statistics-card .h2 {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.text-white-op {
    opacity: 0.9;
    font-weight: 500;
}

/* Monthly Breakdown Table Enhancements */
#monthlyBreakdownTable {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.badge {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Form Controls */
.form-control {
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #5bc0de;
    box-shadow: 0 0 0 0.2rem rgba(91, 192, 222, 0.25);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .statistics-card {
        margin-bottom: 15px;
    }

    .statistics-card .h2 {
        font-size: 1.5rem;
    }

    .statistics-card .fa {
        font-size: 2em;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTables
    App.datatables();

    // Add statistics-card class to all statistic blocks
    $('.row.items-push .block').addClass('statistics-card');

    // Initialize monthly breakdown table
    $('#monthlyBreakdownTable').DataTable({
        columnDefs: [{ orderable: false, targets: [0] }],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']],
        order: [[0, 'asc']]
    });

    console.log('Dashboard JavaScript loaded');

    // Check if elements exist
    console.log('Apply filter button exists:', $('#applyFilter').length);
    console.log('Filter year exists:', $('#filterYear').length);
    console.log('Filter month exists:', $('#filterMonth').length);
    
    // Apply filter button click event
    $('#applyFilter').click(function(e) {
        e.preventDefault();
        console.log('Apply filter button clicked');
        var year = $('#filterYear').val();
        var month = $('#filterMonth').val();
        console.log('Year:', year, 'Month:', month);
        
        // Show loading
        $('body').append('<div id="loading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 18px;"><i class="fa fa-spinner fa-spin"></i> Loading...</div></div>');
        
        // Make AJAX request to get updated statistics
        $.ajax({
            url: '<?php echo e(url("/ap511-check-invoice/dashboard-data")); ?>',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                year: year,
                month: month
            },
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    
                    // Update summary cards
                    $('#totalInvoices').text(numberWithCommas(data.total_invoices));
                    $('#ap511Invoices').text(numberWithCommas(data.ap511_invoices));
                    $('#nonAp511Invoices').text(numberWithCommas(data.non_ap511_invoices));
                    
                    var percentage = data.total_invoices > 0 ? (data.ap511_invoices / data.total_invoices * 100) : 0;
                    $('#ap511Percentage').text(percentage.toFixed(1) + '%');
                    
                    // Update monthly breakdown table
                    var table = $('#monthlyBreakdownTable').DataTable();
                    table.clear();

                    if (Object.keys(data.monthly_breakdown).length === 0) {
                        table.row.add([
                            '<span class="text-muted"><i class="fa fa-info-circle"></i> No data available for the selected period</span>',
                            '', '', '', ''
                        ]).draw();
                    } else {
                        $.each(data.monthly_breakdown, function(index, monthData) {
                            var monthPercentage = monthData.total > 0 ? (monthData.ap511 / monthData.total * 100) : 0;
                            var percentageClass = monthPercentage > 50 ? 'success' : 'warning';

                            table.row.add([
                                '<span class="font-w600 text-primary">' + monthData.month_name + '</span>',
                                '<span class="badge badge-primary">' + numberWithCommas(monthData.total) + '</span>',
                                '<span class="badge badge-success">' + numberWithCommas(monthData.ap511) + '</span>',
                                '<span class="badge badge-danger">' + numberWithCommas(monthData.non_ap511) + '</span>',
                                '<span class="font-w600 text-' + percentageClass + '">' + monthPercentage.toFixed(1) + '%</span>'
                            ]);
                        });
                        table.draw();
                    }
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', error);
                console.log('Status:', status);
                console.log('Response:', xhr.responseText);
                alert('Error loading data. Please check console for details.');
            },
            complete: function() {
                $('#loading').remove();
            }
        });
    });
    
    // Helper function to format numbers with commas
    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>